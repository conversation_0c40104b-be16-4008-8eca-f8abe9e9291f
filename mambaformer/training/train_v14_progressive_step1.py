#!/usr/bin/env python3
"""
V14_Progressive_Step1: 验证V14 baseline
目标: 复现86.35%准确率，作为后续改进的基准
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

# 完全禁用所有tqdm进度条
os.environ['TQDM_DISABLE'] = '1'

# 尝试导入并禁用tqdm（如果存在）
try:
    import tqdm
    tqdm.tqdm.__init__ = lambda self, *args, **kwargs: None
    tqdm.tqdm.__iter__ = lambda self: iter([])
    tqdm.tqdm.update = lambda self, n=1: None
    tqdm.tqdm.close = lambda self: None
except:
    pass

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v14_progressive_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    
    # 清除已有的handlers避免重复
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ],
        force=True
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Progressive - Step 1: Baseline Verification")
    logging.info("="*80)
    
    return log_dir

class REMFocusedLoss(nn.Module):
    """V14的REM/Wake专注损失函数"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        # V14的类权重配置
        self.class_weights = torch.tensor([3.0, 1.0, 1.0, 1.0, 5.0]).to(device)
        # Wake=5.0, N1=1.0, N2=1.0, N3=1.0, REM=3.0
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            inputs = inputs.reshape(-1, inputs.shape[-1])
            targets = targets.reshape(-1)
        
        # Focal Loss
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** 2.0 * ce_loss
        
        # 应用类权重
        weights = self.class_weights[targets]
        weighted_loss = focal_loss * weights
        
        return weighted_loss.mean()

def train_epoch(model, train_loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    for data, target in train_loader:
        data, target = data.to(device), target.to(device)
        
        # 处理序列标签
        if target.dim() > 1:
            target_for_loss = target
            target_for_metric = target[:, target.shape[1]//2]
        else:
            target_for_loss = target
            target_for_metric = target
        
        optimizer.zero_grad()
        output, _ = model(data)
        loss = criterion(output, target_for_loss)
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if output.dim() == 3:
            preds = output[:, output.shape[1]//2, :].argmax(dim=1)
        else:
            preds = output.argmax(dim=1)
        
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target_for_metric.cpu().numpy())
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in data_loader:
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            output, _ = model(data)
            
            if output.dim() == 3:
                preds = output[:, output.shape[1]//2, :].argmax(dim=1)
            else:
                preds = output.argmax(dim=1)
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class F1
    class_f1 = f1_score(all_targets, all_preds, average=None)
    
    return accuracy, f1, kappa, class_f1

def main():
    # Step 1: 使用V14的精确配置
    config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 30,
        'patience': 10
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据文件列表（与V14相同）
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 重要：使用全部数据
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4
    )
    
    # 创建模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = REMFocusedLoss(device)
    optimizer = optim.AdamW(model.parameters(), lr=config['learning_rate'], weight_decay=config['weight_decay'])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=5)
    
    # 训练
    best_val_acc = 0
    best_test_acc = 0
    patience_counter = 0
    
    logging.info("Starting training...")
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(model, train_loader, criterion, optimizer, device)
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1 = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1 = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step(val_acc)
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_test_acc = test_acc
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'test_kappa': test_kappa,
                'config': config
            }, os.path.join(log_dir, 'best_model_baseline.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (Baseline)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Gap to 87%: {0.87 - best_test_acc:.4f} ({(0.87 - best_test_acc)*100:.2f}%)")
    
    # 保存结果
    results = {
        'best_test_accuracy': float(best_test_acc),
        'best_test_f1': float(test_f1),
        'best_test_kappa': float(test_kappa),
        'test_class_f1': test_class_f1.tolist(),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results_baseline.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n✅ Step 1 Complete: Baseline established at {best_test_acc:.4f}")
    logging.info("Ready for Step 2: Adding Progressive Classification")

if __name__ == "__main__":
    main()